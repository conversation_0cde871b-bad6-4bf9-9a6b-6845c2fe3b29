package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3;

import com.decurret_dcp.dcjpy.bcmonitoring.consts.DCFConst;
import com.decurret_dcp.dcjpy.bcmonitoring.exception.UnsupportedTypeException;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.Address;
import org.web3j.abi.datatypes.Bool;
import org.web3j.abi.datatypes.DynamicBytes;
import org.web3j.abi.datatypes.Int;
import org.web3j.abi.datatypes.Type;
import org.web3j.abi.datatypes.Uint;
import org.web3j.abi.datatypes.Utf8String;
import org.web3j.abi.datatypes.generated.*;
import org.web3j.abi.datatypes.primitive.Byte;
import org.web3j.abi.datatypes.primitive.Char;
import org.web3j.abi.datatypes.primitive.Double;
import org.web3j.abi.datatypes.primitive.Float;
import org.web3j.abi.datatypes.primitive.Long;
import org.web3j.abi.datatypes.primitive.Short;

public class AbiTypeConverter {
  private static final Logger log = LoggerFactory.getLogger(AbiTypeConverter.class);
  private static final Map<String, Class<? extends Type<?>>> web3TypeMap = new HashMap<>();

  static {
    // Web3j boolean type
    web3TypeMap.put(DCFConst.BOOL, Bool.class);
    web3TypeMap.put(DCFConst.BOOLEAN, Bool.class);

    // Web3j string type
    web3TypeMap.put(DCFConst.STRING, Utf8String.class);

    // Web3j address type
    web3TypeMap.put(DCFConst.ADDRESS, Address.class);

    // Web3j primitive type
    web3TypeMap.put(DCFConst.DOUBLE, Double.class);
    web3TypeMap.put(DCFConst.FLOAT, Float.class);
    web3TypeMap.put(DCFConst.LONG, Long.class);
    web3TypeMap.put(DCFConst.SHORT, Short.class);
    web3TypeMap.put(DCFConst.CHAR, Char.class);
    web3TypeMap.put(DCFConst.BYTE, Byte.class);

    // Web3j uint types
    web3TypeMap.put(DCFConst.UINT, Uint.class);
    web3TypeMap.put(DCFConst.UINT_8, Uint8.class);
    web3TypeMap.put(DCFConst.UINT_16, Uint16.class);
    web3TypeMap.put(DCFConst.UINT_24, Uint24.class);
    web3TypeMap.put(DCFConst.UINT_32, Uint32.class);
    web3TypeMap.put(DCFConst.UINT_40, Uint40.class);
    web3TypeMap.put(DCFConst.UINT_48, Uint48.class);
    web3TypeMap.put(DCFConst.UINT_56, Uint56.class);
    web3TypeMap.put(DCFConst.UINT_64, Uint64.class);
    web3TypeMap.put(DCFConst.UINT_72, Uint72.class);
    web3TypeMap.put(DCFConst.UINT_80, Uint80.class);
    web3TypeMap.put(DCFConst.UINT_88, Uint88.class);
    web3TypeMap.put(DCFConst.UINT_96, Uint96.class);
    web3TypeMap.put(DCFConst.UINT_104, Uint104.class);
    web3TypeMap.put(DCFConst.UINT_112, Uint112.class);
    web3TypeMap.put(DCFConst.UINT_120, Uint120.class);
    web3TypeMap.put(DCFConst.UINT_128, Uint128.class);
    web3TypeMap.put(DCFConst.UINT_136, Uint136.class);
    web3TypeMap.put(DCFConst.UINT_144, Uint144.class);
    web3TypeMap.put(DCFConst.UINT_152, Uint152.class);
    web3TypeMap.put(DCFConst.UINT_160, Uint160.class);
    web3TypeMap.put(DCFConst.UINT_168, Uint168.class);
    web3TypeMap.put(DCFConst.UINT_176, Uint176.class);
    web3TypeMap.put(DCFConst.UINT_184, Uint184.class);
    web3TypeMap.put(DCFConst.UINT_192, Uint192.class);
    web3TypeMap.put(DCFConst.UINT_200, Uint200.class);
    web3TypeMap.put(DCFConst.UINT_208, Uint208.class);
    web3TypeMap.put(DCFConst.UINT_216, Uint216.class);
    web3TypeMap.put(DCFConst.UINT_224, Uint224.class);
    web3TypeMap.put(DCFConst.UINT_232, Uint232.class);
    web3TypeMap.put(DCFConst.UINT_240, Uint240.class);
    web3TypeMap.put(DCFConst.UINT_248, Uint248.class);
    web3TypeMap.put(DCFConst.UINT_256, Uint256.class);

    // Web3j int types
    web3TypeMap.put(DCFConst.INT, Int.class);
    web3TypeMap.put(DCFConst.INT_8, Int8.class);
    web3TypeMap.put(DCFConst.INT_16, Int16.class);
    web3TypeMap.put(DCFConst.INT_24, Int24.class);
    web3TypeMap.put(DCFConst.INT_32, Int32.class);
    web3TypeMap.put(DCFConst.INT_40, Int40.class);
    web3TypeMap.put(DCFConst.INT_48, Int48.class);
    web3TypeMap.put(DCFConst.INT_56, Int56.class);
    web3TypeMap.put(DCFConst.INT_64, Int64.class);
    web3TypeMap.put(DCFConst.INT_72, Int72.class);
    web3TypeMap.put(DCFConst.INT_80, Int80.class);
    web3TypeMap.put(DCFConst.INT_88, Int88.class);
    web3TypeMap.put(DCFConst.INT_96, Int96.class);
    web3TypeMap.put(DCFConst.INT_104, Int104.class);
    web3TypeMap.put(DCFConst.INT_112, Int112.class);
    web3TypeMap.put(DCFConst.INT_120, Int120.class);
    web3TypeMap.put(DCFConst.INT_128, Int128.class);
    web3TypeMap.put(DCFConst.INT_136, Int136.class);
    web3TypeMap.put(DCFConst.INT_144, Int144.class);
    web3TypeMap.put(DCFConst.INT_152, Int152.class);
    web3TypeMap.put(DCFConst.INT_160, Int160.class);
    web3TypeMap.put(DCFConst.INT_168, Int168.class);
    web3TypeMap.put(DCFConst.INT_176, Int176.class);
    web3TypeMap.put(DCFConst.INT_184, Int184.class);
    web3TypeMap.put(DCFConst.INT_192, Int192.class);
    web3TypeMap.put(DCFConst.INT_200, Int200.class);
    web3TypeMap.put(DCFConst.INT_208, Int208.class);
    web3TypeMap.put(DCFConst.INT_216, Int216.class);
    web3TypeMap.put(DCFConst.INT_224, Int224.class);
    web3TypeMap.put(DCFConst.INT_232, Int232.class);
    web3TypeMap.put(DCFConst.INT_240, Int240.class);
    web3TypeMap.put(DCFConst.INT_248, Int248.class);
    web3TypeMap.put(DCFConst.INT_256, Int256.class);

    // Web3j dynamic bytes type
    web3TypeMap.put(DCFConst.BYTES, DynamicBytes.class);

    // Web3j bytes types
    web3TypeMap.put(DCFConst.BYTES_1, Bytes1.class);
    web3TypeMap.put(DCFConst.BYTES_2, Bytes2.class);
    web3TypeMap.put(DCFConst.BYTES_3, Bytes3.class);
    web3TypeMap.put(DCFConst.BYTES_4, Bytes4.class);
    web3TypeMap.put(DCFConst.BYTES_5, Bytes5.class);
    web3TypeMap.put(DCFConst.BYTES_6, Bytes6.class);
    web3TypeMap.put(DCFConst.BYTES_7, Bytes7.class);
    web3TypeMap.put(DCFConst.BYTES_8, Bytes8.class);
    web3TypeMap.put(DCFConst.BYTES_9, Bytes9.class);
    web3TypeMap.put(DCFConst.BYTES_10, Bytes10.class);
    web3TypeMap.put(DCFConst.BYTES_11, Bytes11.class);
    web3TypeMap.put(DCFConst.BYTES_12, Bytes12.class);
    web3TypeMap.put(DCFConst.BYTES_13, Bytes13.class);
    web3TypeMap.put(DCFConst.BYTES_14, Bytes14.class);
    web3TypeMap.put(DCFConst.BYTES_15, Bytes15.class);
    web3TypeMap.put(DCFConst.BYTES_16, Bytes16.class);
    web3TypeMap.put(DCFConst.BYTES_17, Bytes17.class);
    web3TypeMap.put(DCFConst.BYTES_18, Bytes18.class);
    web3TypeMap.put(DCFConst.BYTES_19, Bytes19.class);
    web3TypeMap.put(DCFConst.BYTES_20, Bytes20.class);
    web3TypeMap.put(DCFConst.BYTES_21, Bytes21.class);
    web3TypeMap.put(DCFConst.BYTES_22, Bytes22.class);
    web3TypeMap.put(DCFConst.BYTES_23, Bytes23.class);
    web3TypeMap.put(DCFConst.BYTES_24, Bytes24.class);
    web3TypeMap.put(DCFConst.BYTES_25, Bytes25.class);
    web3TypeMap.put(DCFConst.BYTES_26, Bytes26.class);
    web3TypeMap.put(DCFConst.BYTES_27, Bytes27.class);
    web3TypeMap.put(DCFConst.BYTES_28, Bytes28.class);
    web3TypeMap.put(DCFConst.BYTES_29, Bytes29.class);
    web3TypeMap.put(DCFConst.BYTES_30, Bytes30.class);
    web3TypeMap.put(DCFConst.BYTES_31, Bytes31.class);
    web3TypeMap.put(DCFConst.BYTES_32, Bytes32.class);
  }

  /**
   * Converts a Solidity type string into a corresponding {@link TypeReference} instance used by
   * Web3j.
   *
   * <p>This method looks up the given Solidity type in a predefined type mapping, and creates a
   * {@code TypeReference<?>} for use in encoding/decoding smart contract data.
   *
   * @param solidityType the Solidity type as a string (e.g., {@code "uint256"}, {@code "string"},
   *     {@code "bytes32"})
   * @param isIndexed whether the parameter is indexed (applicable for event parameters)
   * @return a {@code TypeReference<?>} corresponding to the given Solidity type
   * @throws UnsupportedTypeException if the Solidity type is not supported or not mapped
   */
  public static TypeReference<?> convertType(String solidityType, boolean isIndexed) {
    if (web3TypeMap.containsKey(solidityType)) {
      Class<? extends Type<?>> clazz = web3TypeMap.get(solidityType);
      return createTypeReference(clazz, isIndexed);
    }
    String errorMessage = "Unsupported type: " + solidityType;
    log.error(errorMessage);
    throw new UnsupportedTypeException(errorMessage);
  }

  /**
   * Creates a {@link TypeReference} instance for the given Web3j ABI type class.
   *
   * <p>This method allows dynamic creation of {@code TypeReference<T>} without the need to manually
   * declare each specific type (e.g., {@code Uint256}, {@code Address}, etc.).
   *
   * @param clazz the class representing a Web3j ABI type (e.g., {@code Uint8.class}, {@code
   *     Utf8String.class})
   * @param isIndexed true if the parameter should be marked as indexed (used in event logs)
   * @param <T> the Web3j ABI type extending {@code Type<?>}
   * @return a {@code TypeReference<T>} instance corresponding to the given type
   */
  private static <T extends Type<?>> TypeReference<T> createTypeReference(
      Class<T> clazz, boolean isIndexed) {
    return new TypeReference<>(isIndexed) {
      @Override
      public java.lang.reflect.Type getType() {
        return clazz;
      }
    };
  }
}
