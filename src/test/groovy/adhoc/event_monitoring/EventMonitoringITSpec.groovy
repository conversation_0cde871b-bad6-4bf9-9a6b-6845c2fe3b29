package adhoc.event_monitoring

import adhoc.base.BaseAdhocITSpec
import adhoc.helper.AdhocHelper
import adhoc.mock.EventMockFactory
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import io.reactivex.Flowable
import io.reactivex.processors.PublishProcessor
import java.time.Instant
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicInteger
import org.json.JSONObject
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.CommandLineRunner
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.ApplicationContext
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean
import org.web3j.protocol.core.methods.response.EthLog
import org.web3j.protocol.websocket.events.NewHeadsNotification

@SpringBootTest(
classes = [BcmonitoringApplication.class],
webEnvironment = SpringBootTest.WebEnvironment.NONE
)
@ActiveProfiles("test")
class EventMonitoringITSpec extends BaseAdhocITSpec {

	@Autowired
	ApplicationContext applicationContext

	@MockitoSpyBean
	Web3jConfig web3jConfig

	@Autowired
	BcmonitoringConfigurationProperties properties

	@Autowired
	AbiParser abiParser

	@Override
	Web3jConfig getWeb3jConfig() {
		return web3jConfig
	}

	def setupSpec() {
		setupSpecCommon()
	}

	def cleanupSpec() {
		cleanupSpecCommon()
	}

	def setup() {
		setupCommon()
		// Upload real ABI files to S3
		AdhocHelper.uploadHardhatAbiFiles(s3Client, TEST_BUCKET, "3000", [
			"AccessCtrl",
			"Token",
			"Account",
			"Provider"
		])
	}

	def cleanup() {
		cleanupCommon()
	}

	/**
	 * Should detects and processes events from new blockchain blocks
	 * Verifies service correctly detects and processes events
	 * Expected: Events extracted, parsed, and saved to DynamoDB
	 */
	def "Should detects and processes events from new blockchain blocks"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available"
		// Starting from block 1000, creating 3 notifications (blocks 1000, 1001, 1002)
		def mockNotifications = createMockNewHeadsNotifications(1000L, 3)
		setUpEventStream(mockNotifications)
		// Setup mock Web3j to return blocks with transactions and events
		def eventLogConfigs = [
			[logType: 'addProviderRole', txHash: '0xabc123', blockNumber: 1000L],
			[logType: 'addTokenByProvider', txHash: '0xdef456', blockNumber: 1001L],
			[logType: 'roleAdminChanged', txHash: '0x123abc', blockNumber: 1002L]
		]
		setupMockWeb3jWithEvents(eventLogConfigs)

		setUpPendingEvent(Collections.emptyList())

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "No exceptions are thrown"
		noExceptionThrown()

		and: "Service starts and processes ABI files successfully"
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Started bc monitoring") }
		assert messages.any { it.contains("Monitoring events...") }

		and: "New events are processed"
		assert messages.any { it.contains("Event found tx_hash=") }
		assert messages.any { it.contains("Event parsed tx_hash=") && it.contains("name=") }
		assert messages.any { it.contains("Success to register event") }
		assert messages.any { it.contains("Success to register block number") }
		assert messages.any { it.contains("Success to process new transactions") }

		and: "Provider events are processed and saved to DynamoDB"
		// Check if events were saved to DynamoDB
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		println("Events found in DynamoDB: ${eventsInDb?.size() ?: 'null'}")
		if (eventsInDb != null) {
			eventsInDb.each { event ->
				println("Event: ${event}")
			}
		}

		and: "Block height is updated"
		def blockHeightInDb = AdhocHelper.scanBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
		println("Block height in DynamoDB: ${blockHeightInDb}")
		// Verify that block height was processed
		blockHeightInDb != null
	}

	/**
	 * Should process pending transactions from specified block height
	 * Verifies service correctly processes pending transactions
	 * Expected: Pending transactions are processed and saved to DynamoDB
	 */
	def "Should process pending transactions from specified block height"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available"

		setUpEventStream(Collections.emptyList())

		// Setup mock pending events
		def mockPendingEventLogs = createMockPendingEventLogs([
			"addProviderRole",
			"addTokenByProvider"
		], 200L, "0xabc")
		setUpPendingEvent(mockPendingEventLogs)

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "No exceptions are thrown"
		noExceptionThrown()

		and: "Service starts and processes ABI files successfully"
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Started bc monitoring") }
		assert messages.any { it.contains("Monitoring events...") }

		and: "Pending events are processed"
		assert messages.any { it.contains("Retrieved") && it.contains("logs from block height") }
		assert messages.any { it.contains("Event found tx_hash=") }
		assert messages.any { it.contains("Event parsed tx_hash=") && it.contains("name=") }
		assert messages.any { it.contains("Success to register event") }
		assert messages.any { it.contains("Success to process pending transactions") }

		and: "Provider events are processed and saved to DynamoDB"
		// Check if events were saved to DynamoDB
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		println("Events found in DynamoDB: ${eventsInDb?.size() ?: 'null'}")
		if (eventsInDb != null) {
			eventsInDb.each { event ->
				println("Event: ${event}")
			}
		}

		and: "Block height is updated"
		def blockHeightInDb = AdhocHelper.scanBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
		println("Block height in DynamoDB: ${blockHeightInDb}")
		// Verify that block height was processed
		blockHeightInDb != null
	}

	/**
	 * Should event data correctly parsed into indexed and non-indexed values
	 * Verifies service correctly parses event data
	 * Expected: Event data is correctly parsed into indexed and non-indexed values
	 */
	def "Should event data correctly parsed into indexed and non-indexed values"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available"
		// Starting from block 1000, creating 3 notifications (blocks 1000, 1001, 1002)
		def mockNotifications = createMockNewHeadsNotifications(1000L, 4)
		setUpEventStream(mockNotifications)
		// Setup mock Web3j to return blocks with transactions and events
		def eventLogConfigs = [
			[logType: 'addProviderRole', txHash: '0xabc123', blockNumber: 1000L],
			[logType: 'addTokenByProvider', txHash: '0xdef456', blockNumber: 1001L],
			[logType: 'roleAdminChanged', txHash: '0x123abc', blockNumber: 1002L],
			[logType: 'modAccount', txHash: '0x123abd', blockNumber: 1003L]
		]
		setupMockWeb3jWithEvents(eventLogConfigs)

		// Setup mock pending events
		def mockPendingEventLogs = createMockPendingEventLogs([
			"addProviderRole",
			"addTokenByProvider"
		], 200L, "0xabc")
		setUpPendingEvent(mockPendingEventLogs)

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "No exceptions are thrown"
		noExceptionThrown()

		and: "Service starts and processes ABI files successfully"
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Started bc monitoring") }
		assert messages.any { it.contains("Monitoring events...") }

		and: "Event data is correctly parsed"
		assert messages.any { it.contains("Event parsed tx_hash=") && it.contains("name=") }

		and: "Event stored in dynamodb is correctly parsed"
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)

		//Check data of pending transaction addProviderRole Event
		def addProviderRolePendingEvent = eventsInDb.find { event ->
			event.get("name").s() == "AddProviderRole" &&
					event.get("transactionHash").s() == "0xabc134"
		}

		assert addProviderRolePendingEvent != null

		assert addProviderRolePendingEvent.get("indexedValues").s().contains("providerId")
		assert addProviderRolePendingEvent.get("nonIndexedValues").s().contains("providerEoa")
		assert addProviderRolePendingEvent.get("nonIndexedValues").s().contains("traceId")

		assert AdhocHelper.compareHexByteArray(new JSONObject(addProviderRolePendingEvent.get("indexedValues").s()).getJSONArray("providerId"), EventMockFactory.PROVIDER_ID_HEX)
		assert new JSONObject(addProviderRolePendingEvent.get("nonIndexedValues").s()).getString("providerEoa") == EventMockFactory.PROVIDER_EOA_HEX_20_BYTES
		assert AdhocHelper.compareHexByteArray(new JSONObject(addProviderRolePendingEvent.get("nonIndexedValues").s()).getJSONArray("traceId"), EventMockFactory.TRACE_ADD_PROVIDER)
		assert addProviderRolePendingEvent.get("logIndex").n() == "0"
		assert addProviderRolePendingEvent.get("blockTimestamp").n() == "**********"

		//Check data of new transaction addProviderRole Event
		def addProviderRoleNewEvent = eventsInDb.find { event ->
			event.get("name").s() == "AddProviderRole" &&
					event.get("transactionHash").s() == "0xabc123134"
		}

		assert addProviderRoleNewEvent != null

		assert addProviderRoleNewEvent.get("indexedValues").s().contains("providerId")
		assert addProviderRoleNewEvent.get("nonIndexedValues").s().contains("providerEoa")
		assert addProviderRoleNewEvent.get("nonIndexedValues").s().contains("traceId")

		assert AdhocHelper.compareHexByteArray(new JSONObject(addProviderRoleNewEvent.get("indexedValues").s()).getJSONArray("providerId"), EventMockFactory.PROVIDER_ID_HEX)
		assert new JSONObject(addProviderRoleNewEvent.get("nonIndexedValues").s()).getString("providerEoa") == EventMockFactory.PROVIDER_EOA_HEX_20_BYTES
		assert AdhocHelper.compareHexByteArray(new JSONObject(addProviderRoleNewEvent.get("nonIndexedValues").s()).getJSONArray("traceId"), EventMockFactory.TRACE_ADD_PROVIDER)
		assert addProviderRoleNewEvent.get("logIndex").n() == "0"
		assert addProviderRoleNewEvent.get("blockTimestamp").n() == "**********"

		//Check data of pending transaction addProviderRole Event
		def addTokenByProviderPendingEvent = eventsInDb.find { event ->
			event.get("name").s() == "AddTokenByProvider" && event.get("transactionHash").s() == "0xdef456135"
		}

		assert addTokenByProviderPendingEvent != null

		//Assert data of pending transaction addTokenByProvider Event
		assert addTokenByProviderPendingEvent.get("indexedValues").s().contains("providerId")
		assert addTokenByProviderPendingEvent.get("nonIndexedValues").s().contains("tokenId")
		assert addTokenByProviderPendingEvent.get("nonIndexedValues").s().contains("traceId")

		assert AdhocHelper.compareHexByteArray(new JSONObject(addTokenByProviderPendingEvent.get("indexedValues").s()).getJSONArray("providerId"), EventMockFactory.PROVIDER_ID_HEX)
		assert AdhocHelper.compareHexByteArray(new JSONObject(addTokenByProviderPendingEvent.get("nonIndexedValues").s()).getJSONArray("tokenId"), EventMockFactory.TOKEN_ID_HEX)
		assert AdhocHelper.compareHexByteArray(new JSONObject(addTokenByProviderPendingEvent.get("nonIndexedValues").s()).getJSONArray("traceId"), EventMockFactory.TRACE_ADD_TOKEN_HEX)
		assert addTokenByProviderPendingEvent.get("logIndex").n() == "1"
		assert addTokenByProviderPendingEvent.get("blockTimestamp").n() == "**********"

		//Check data of new transaction addProviderRole Event
		def addTokenByProviderNewEvent = eventsInDb.find { event ->
			event.get("name").s() == "AddTokenByProvider" && event.get("transactionHash").s() == "0xdef456135"
		}

		assert addTokenByProviderNewEvent != null

		//Assert data of pending transaction addTokenByProvider Event
		assert addTokenByProviderNewEvent.get("indexedValues").s().contains("providerId")
		assert addTokenByProviderNewEvent.get("nonIndexedValues").s().contains("tokenId")
		assert addTokenByProviderNewEvent.get("nonIndexedValues").s().contains("traceId")

		assert AdhocHelper.compareHexByteArray(new JSONObject(addTokenByProviderNewEvent.get("indexedValues").s()).getJSONArray("providerId"), EventMockFactory.PROVIDER_ID_HEX)
		assert AdhocHelper.compareHexByteArray(new JSONObject(addTokenByProviderNewEvent.get("nonIndexedValues").s()).getJSONArray("tokenId"), EventMockFactory.TOKEN_ID_HEX)
		assert AdhocHelper.compareHexByteArray(new JSONObject(addTokenByProviderNewEvent.get("nonIndexedValues").s()).getJSONArray("traceId"), EventMockFactory.TRACE_ADD_TOKEN_HEX)
		assert addTokenByProviderNewEvent.get("logIndex").n() == "1"
		assert addTokenByProviderNewEvent.get("blockTimestamp").n() == "**********"
	}

	/**
	 * Should extracts traceId from event non-indexed values when present
	 * Verifies traceId correctly extracted and logged
	 * Expected: TraceId is correctly extracted from event data and logged
	 */
	def "Should extracts traceId from event non-indexed values when present"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available"
		// Starting from block 1000, creating 3 notifications (blocks 1000, 1001, 1002)
		def mockNotifications = createMockNewHeadsNotifications(1000L, 3)
		setUpEventStream(mockNotifications)
		// Setup mock Web3j to return blocks with transactions and events
		def eventLogConfigs = [
			[logType: 'addProviderRole', txHash: '0xabc123', blockNumber: 1000L],
			[logType: 'addTokenByProvider', txHash: '0xdef456', blockNumber: 1001L],
			[logType: 'roleAdminChanged', txHash: '0x123abc', blockNumber: 1002L]
		]
		setupMockWeb3jWithEvents(eventLogConfigs)

		// Setup mock pending events
		def mockPendingEventLogs = createMockPendingEventLogs([
			"addProviderRole",
			"addTokenByProvider"
		], 200L, "0xabc")
		setUpPendingEvent(mockPendingEventLogs)

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "No exceptions are thrown"
		noExceptionThrown()

		and: "Service starts and processes ABI files successfully"
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Started bc monitoring") }
		assert messages.any { it.contains("Monitoring events...") }

		and: "Event data is correctly parsed"
		assert messages.any { it.contains("Event parsed tx_hash=") && it.contains("name=") }

		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		//Check data of pending transaction addProviderRole Event
		def addProviderRolePendingEvent = eventsInDb.find { event ->
			event.get("name").s() == "AddProviderRole" &&
					event.get("transactionHash").s() == "0xabc134"
		}

		assert addProviderRolePendingEvent != null

		assert addProviderRolePendingEvent.get("indexedValues").s().contains("providerId")
		assert addProviderRolePendingEvent.get("nonIndexedValues").s().contains("providerEoa")
		assert addProviderRolePendingEvent.get("nonIndexedValues").s().contains("traceId")

		assert AdhocHelper.compareHexByteArray(new JSONObject(addProviderRolePendingEvent.get("indexedValues").s()).getJSONArray("providerId"), EventMockFactory.PROVIDER_ID_HEX)
		assert new JSONObject(addProviderRolePendingEvent.get("nonIndexedValues").s()).getString("providerEoa") == EventMockFactory.PROVIDER_EOA_HEX_20_BYTES
		assert AdhocHelper.compareHexByteArray(new JSONObject(addProviderRolePendingEvent.get("nonIndexedValues").s()).getJSONArray("traceId"), EventMockFactory.TRACE_ADD_PROVIDER)
		assert addProviderRolePendingEvent.get("logIndex").n() == "0"
		assert addProviderRolePendingEvent.get("blockTimestamp").n() == "**********"

		//Check data of new transaction addProviderRole Event
		def addProviderRoleNewEvent = eventsInDb.find { event ->
			event.get("name").s() == "AddProviderRole" &&
					event.get("transactionHash").s() == "0xabc123134"
		}

		assert addProviderRoleNewEvent != null

		assert addProviderRoleNewEvent.get("indexedValues").s().contains("providerId")
		assert addProviderRoleNewEvent.get("nonIndexedValues").s().contains("providerEoa")
		assert addProviderRoleNewEvent.get("nonIndexedValues").s().contains("traceId")

		assert AdhocHelper.compareHexByteArray(new JSONObject(addProviderRoleNewEvent.get("indexedValues").s()).getJSONArray("providerId"), EventMockFactory.PROVIDER_ID_HEX)
		assert new JSONObject(addProviderRoleNewEvent.get("nonIndexedValues").s()).getString("providerEoa") == EventMockFactory.PROVIDER_EOA_HEX_20_BYTES
		assert AdhocHelper.compareHexByteArray(new JSONObject(addProviderRoleNewEvent.get("nonIndexedValues").s()).getJSONArray("traceId"), EventMockFactory.TRACE_ADD_PROVIDER)
		assert addProviderRoleNewEvent.get("logIndex").n() == "0"
		assert addProviderRoleNewEvent.get("blockTimestamp").n() == "**********"

		//Check data of pending transaction addProviderRole Event
		def addTokenByProviderPendingEvent = eventsInDb.find { event ->
			event.get("name").s() == "AddTokenByProvider" && event.get("transactionHash").s() == "0xdef456135"
		}

		assert addTokenByProviderPendingEvent != null

		//Assert data of pending transaction addTokenByProvider Event
		assert addTokenByProviderPendingEvent.get("indexedValues").s().contains("providerId")
		assert addTokenByProviderPendingEvent.get("nonIndexedValues").s().contains("tokenId")
		assert addTokenByProviderPendingEvent.get("nonIndexedValues").s().contains("traceId")

		assert AdhocHelper.compareHexByteArray(new JSONObject(addTokenByProviderPendingEvent.get("indexedValues").s()).getJSONArray("providerId"), EventMockFactory.PROVIDER_ID_HEX)
		assert AdhocHelper.compareHexByteArray(new JSONObject(addTokenByProviderPendingEvent.get("nonIndexedValues").s()).getJSONArray("tokenId"), EventMockFactory.TOKEN_ID_HEX)
		assert AdhocHelper.compareHexByteArray(new JSONObject(addTokenByProviderPendingEvent.get("nonIndexedValues").s()).getJSONArray("traceId"), EventMockFactory.TRACE_ADD_TOKEN_HEX)
		assert addTokenByProviderPendingEvent.get("logIndex").n() == "1"
		assert addTokenByProviderPendingEvent.get("blockTimestamp").n() == "**********"

		//Check data of new transaction addProviderRole Event
		def addTokenByProviderNewEvent = eventsInDb.find { event ->
			event.get("name").s() == "AddTokenByProvider" && event.get("transactionHash").s() == "0xdef456135"
		}

		assert addTokenByProviderNewEvent != null

		//Assert data of pending transaction addTokenByProvider Event
		assert addTokenByProviderNewEvent.get("indexedValues").s().contains("providerId")
		assert addTokenByProviderNewEvent.get("nonIndexedValues").s().contains("tokenId")
		assert addTokenByProviderNewEvent.get("nonIndexedValues").s().contains("traceId")

		assert AdhocHelper.compareHexByteArray(new JSONObject(addTokenByProviderNewEvent.get("indexedValues").s()).getJSONArray("providerId"), EventMockFactory.PROVIDER_ID_HEX)
		assert AdhocHelper.compareHexByteArray(new JSONObject(addTokenByProviderNewEvent.get("nonIndexedValues").s()).getJSONArray("tokenId"), EventMockFactory.TOKEN_ID_HEX)
		assert AdhocHelper.compareHexByteArray(new JSONObject(addTokenByProviderNewEvent.get("nonIndexedValues").s()).getJSONArray("traceId"), EventMockFactory.TRACE_ADD_TOKEN_HEX)
		assert addTokenByProviderNewEvent.get("logIndex").n() == "1"
		assert addTokenByProviderNewEvent.get("blockTimestamp").n() == "**********"
	}

	/**
	 * Should run normally when block timestamp is equal or less than allowableDiffSeconds
	 * Verifies service doesn't log warning when block timestamp is within allowable range
	 * Expected: Service doesn't logs "Block {} is delayed by more than {} seconds"
	 */
	def "Should run normally when timestamp is equal or less than allowable block timestamp different seconds"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available"
		// Starting from block 1000, creating 3 notifications (blocks 1000, 1001, 1002)
		def mockNotifications = createMockNewHeadsNotifications(1000L, 3)
		setUpEventStream(mockNotifications)
		// Setup mock Web3j to return blocks with transactions and events
		def eventLogConfigs = [
			[logType: 'addProviderRole', txHash: '0xabc123', blockNumber: 1000L, timestamp: Instant.now().epochSecond + 16] // Set block timestamp to be within allowable range
		]
		setupMockWeb3jWithEvents(eventLogConfigs)

		// Setup mock pending events
		def mockPendingEventLogs = createMockPendingEventLogs([
			"addProviderRole",
			"addTokenByProvider"
		], 200L, "0xabc")
		setUpPendingEvent(mockPendingEventLogs)

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "No exceptions are thrown"
		noExceptionThrown()

		and: "Service starts and processes ABI files successfully"
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Started bc monitoring") }
		assert messages.any { it.contains("Monitoring events...") }

		and: "Event data is correctly parsed"
		assert messages.any { it.contains("Event parsed tx_hash=") && it.contains("name=") }

		and: "No warning is logged for block timestamp difference"
		def expectedLog = String.format("Block %s is delayed by more than %s seconds",
				"1000", properties.subscription.getAllowableBlockTimestampDiffSec())
		assert messages.any { !it.contains(expectedLog) }
	}

	/**
	 * Should event that don't contain traceId are processed successfully and logs with empty traceId in MDC
	 * Verifies service correctly processes events that don't contain traceId and logs with empty traceId in MDC
	 * Expected: Events are processed successfully and logs with empty traceId in MDC
	 */
	def "Should event that don't contain traceId are processed successfully and logs with empty traceId in MDC"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available"
		// Starting from block 1000, creating 3 notifications (blocks 1000, 1001, 1002)
		def mockNotifications = createMockNewHeadsNotifications(1000L, 3)
		setUpEventStream(mockNotifications)
		// Setup mock Web3j to return blocks with transactions and events
		def eventLogConfigs = [
			[logType: 'addProviderRole', txHash: '0xabc123', blockNumber: 1000L],
			[logType: 'addTokenByProvider', txHash: '0xdef456', blockNumber: 1001L],
			[logType: 'roleAdminChanged', txHash: '0x123abc', blockNumber: 1002L]
		]
		setupMockWeb3jWithEvents(eventLogConfigs)

		// Setup mock pending events
		def mockPendingEventLogs = createMockPendingEventLogs(["roleAdminChanged"], 900L, "0xnotraceId")
		setUpPendingEvent(mockPendingEventLogs)

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "No exceptions are thrown"
		noExceptionThrown()

		and: "Service starts and processes ABI files successfully"
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Started bc monitoring") }
		assert messages.any { it.contains("Monitoring events...") }

		and: "Event data is correctly parsed"
		assert messages.any { it.contains("Event parsed tx_hash=0xnotraceId") && it.contains("name=") }

		and: "Event are processed and saved to DynamoDB"
		// Check if events were saved to DynamoDB
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		def roleAdminChangedEvent = eventsInDb.find { event ->
			event.get("name").s() == "RoleAdminChanged"
		}
		assert roleAdminChangedEvent != null

		and: "Event RoleAdminChanged is logged with empty traceId in MDC"
		def logEvents = logAppender.list
		def eventLogWithTxHash = logEvents.find {
			it.message == "Success to register event" &&
					it.getMDCPropertyMap()?.get("tx_hash")?.startsWith("0xnotraceId")
		}
		assert eventLogWithTxHash != null
		assert eventLogWithTxHash.getMDCPropertyMap().get("trace_id") == ""
		assert eventLogWithTxHash.getMDCPropertyMap().get("tx_hash").startsWith("0xnotraceId")
		assert eventLogWithTxHash.getMDCPropertyMap().get("event_name") == "RoleAdminChanged"
		assert eventLogWithTxHash.getMDCPropertyMap().get("block_height") == "900"
		assert eventLogWithTxHash.getMDCPropertyMap().get("log_index") == "0"
	}

	/**
	 * Should run normally when timestamp against allowable block timestamp different seconds
	 * Verifies service runs normally when block timestamp is against allowable range
	 * Expected: Service runs normally and warning logs "Block {} is delayed by more than {} seconds"
	 */
	def "Should run normally when timestamp against allowable block timestamp different seconds"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available"
		// Starting from block 1000, creating 3 notifications (blocks 1000, 1001, 1002)
		def mockNotifications = createMockNewHeadsNotifications(1000L, 3)
		setUpEventStream(mockNotifications)
		// Setup mock Web3j to return blocks with transactions and events
		def eventLogConfigs = [
			[logType: 'addProviderRole', txHash: '0xabc123', blockNumber: 1000L],
			[logType: 'addTokenByProvider', txHash: '0xdef456', blockNumber: 1001L],
			[logType: 'roleAdminChanged', txHash: '0x123abc', blockNumber: 1002L]
		]
		setupMockWeb3jWithEvents(eventLogConfigs)

		// Setup mock pending events
		def mockPendingEventLogs = createMockPendingEventLogs([
			"addProviderRole",
			"addTokenByProvider"
		], 200L, "0xabc")
		setUpPendingEvent(mockPendingEventLogs)

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "No exceptions are thrown"
		noExceptionThrown()

		and: "Service starts and processes ABI files successfully"
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Started bc monitoring") }
		assert messages.any { it.contains("Monitoring events...") }

		and: "Warning is logged for block timestamp difference"
		def expectedLog = String.format("Block %s is delayed by more than %s seconds",
				"1000", properties.subscription.getAllowableBlockTimestampDiffSec())
		assert messages.any { it.contains(expectedLog) }

		and: "New events are processed"
		assert messages.any { it.contains("Event found tx_hash=") }
		assert messages.any { it.contains("Event parsed tx_hash=") && it.contains("name=") }
		assert messages.any { it.contains("Success to register event") }
		assert messages.any { it.contains("Success to register block number") }
		assert messages.any { it.contains("Success to process new transactions") }
	}

	/**
	 * Should handles events that don't match any loaded ABI definitions
	 * Verifies service correctly handles events that don't match any loaded ABI definitions
	 * Expected: Service continues running and logs "Event definition not found in ABI"
	 */
	def "Should handles events that don't match any loaded ABI definitions"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available"
		// Starting from block 1000, creating 3 notifications (blocks 1000, 1001, 1002)
		def mockNotifications = createMockNewHeadsNotifications(1000L, 3)
		setUpEventStream(mockNotifications)
		// Setup mock Web3j to return blocks with transactions and events
		def eventLogConfigs = [
			[logType: 'addProviderRole', txHash: '0xabc123', blockNumber: 1000L],
			[logType: 'addTokenByProvider', txHash: '0xdef456', blockNumber: 1001L],
			[logType: 'roleAdminChanged', txHash: '0x123abc', blockNumber: 1002L]
		]
		setupMockWeb3jWithEvents(eventLogConfigs)

		// Setup mock pending events
		def mockPendingEventLogs = createMockPendingEventLogs([
			"addProviderRole",
			"addTokenByProvider"
		], 200L, "0xabc")
		setUpPendingEvent(mockPendingEventLogs)

		// Clear ABI files from S3 bucket
		clearS3Bucket()
		assert s3Client.listObjectsV2 { it.bucket(TEST_BUCKET) }.contents().isEmpty()

		// Clear ABI parser store
		abiParser.contractEventStore.clear()
		abiParser.contractAddresses.clear()

		// upload ABI files that don't match any events in mock Web3j
		AdhocHelper.uploadHardhatAbiFiles(s3Client, TEST_BUCKET, "3000", ["Issuer"])

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "No exceptions are thrown"
		noExceptionThrown()

		and: "Service starts and processes ABI files successfully"
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Started bc monitoring") }
		assert messages.any { it.contains("Monitoring events...") }

		and: "Event definition not found in ABI is logged"
		def combined = logAppender.list.collect {
			def message = it.throwableProxy?.message ?: ""
			def stacktrace = it.throwableProxy?.stackTraceElementProxyArray?.collect { it.toString() }?.join(" ") ?: ""
			"$message $stacktrace"
		}

		assert combined.any { it.contains("Event definition not found in ABI") }
	}

	/**
	 * Should handles websocket disconnect during active subscription
	 * Verifies service correctly handles websocket disconnect while actively receiving events
	 * Expected: Service detects disconnect, logs error, and restarts monitoring
	 */
	def "Should handles websocket disconnect during active subscription"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available"
		// Create a flowable that emits some notifications then errors to simulate disconnect
		def index = new AtomicInteger(0)
		def disconnectFlowable = Flowable.defer({
			def processor = PublishProcessor.<NewHeadsNotification>create()

			scheduler.scheduleWithFixedDelay({
				int i = index.getAndIncrement()
				if (i < 2) {
					// Emit 2 normal notifications first
					def mockNotification = createMockNewHeadsNotifications(1000L + i, 1)[0]
					processor.onNext(mockNotification)
				} else if (i == 2) {
					// Then simulate disconnect with error
					processor.onError(new RuntimeException("WebSocket connection lost"))
				}
			}, 1, 2, TimeUnit.SECONDS)

			return processor
		})

		// Setup mock Web3j to return the disconnect flowable
		web3j.newHeadsNotifications() >> disconnectFlowable
		// Setup mock Web3j to return blocks with transactions and events
		def eventLogConfigs = [
			[logType: 'addProviderRole', txHash: '0xabc123', blockNumber: 1000L],
			[logType: 'addTokenByProvider', txHash: '0xdef456', blockNumber: 1001L],
			[logType: 'roleAdminChanged', txHash: '0x123abc', blockNumber: 1002L]
		]
		setupMockWeb3jWithEvents(eventLogConfigs)

		def mockPendingEventLogs = createMockPendingEventLogs([
			"addProviderRole",
			"addTokenByProvider"
		], 200L, "0xabc")
		setUpPendingEvent(mockPendingEventLogs)

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "No exceptions are thrown"
		noExceptionThrown()

		and: "Disconnect is detected and handled properly"
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Subscription error") }
		assert messages.any { it.contains("Error in monitoring: Error subscribing to blockchain events") }
		assert messages.any { it.contains("Restarting bc monitoring") }

		and: "Service processes some events before disconnect"
		assert messages.any { it.contains("Event found tx_hash=") }
		assert messages.any { it.contains("Success to register event") }
	}

	/**
	 * Should handles websocket connection failure at startup
	 * Verifies service correctly handles websocket connection failure when trying to create initial connection
	 * Expected: Service logs connection failure and attempts to restart
	 */
	def "Should handles websocket connection failure at startup"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available"
		// Mock Web3j to throw exception when creating subscription
		web3j.newHeadsNotifications() >> { throw new RuntimeException("Connection refused") }
		// Setup mock Web3j to return blocks with transactions and events
		def eventLogConfigs = [
			[logType: 'addProviderRole', txHash: '0xabc123', blockNumber: 1000L],
			[logType: 'addTokenByProvider', txHash: '0xdef456', blockNumber: 1001L],
			[logType: 'roleAdminChanged', txHash: '0x123abc', blockNumber: 1002L]
		]
		setupMockWeb3jWithEvents(eventLogConfigs)

		def mockPendingEventLogs = createMockPendingEventLogs([
			"addProviderRole",
			"addTokenByProvider"
		], 200L, "0xabc")
		setUpPendingEvent(mockPendingEventLogs)

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "No exceptions are thrown"
		noExceptionThrown()

		and: "Connection failure at startup is handled properly"
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Failed to create Web3j subscription") }
		assert messages.any { it.contains("Error in monitoring") }
		assert messages.any { it.contains("Restarting bc monitoring") }

		and: "Service does not process any events before restart"
		assert !messages.any { it.contains("Event found tx_hash=") }
		assert !messages.any { it.contains("Success to register event") }
	}

	/**
	 * Should rejects events with missing transaction hash in pending events
	 * Verifies service rejects events with missing transaction hash
	 * Expected: Service rejects events with missing transaction hash, logs "Event transaction hash is zero" and monitoring continues with retry
	 */
	def "Should rejects events with missing transaction hash in pending events"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available"
		// Starting from block 1000, creating 3 notifications (blocks 1000, 1001, 1002)
		def mockNotifications = createMockNewHeadsNotifications(1000L, 3)
		setUpEventStream(mockNotifications)
		// Setup mock Web3j to return blocks with transactions and events
		def eventLogConfigs = [
			[logType: 'addProviderRole', txHash: '0xabc123', blockNumber: 1000L],
			[logType: 'addTokenByProvider', txHash: '0xdef456', blockNumber: 1001L],
			[logType: 'roleAdminChanged', txHash: '0x123abc', blockNumber: 1002L]
		]
		setupMockWeb3jWithEvents(eventLogConfigs)

		// Setup mock pending events with missing transaction hash
		// Create mock pending event logs from two different blocks
		def mockPendingEventLogsBlock200 = createMockPendingEventLogs(["roleGranted"], 200L, null)
		def mockPendingEventLogsBlock300 = createMockPendingEventLogs(["addProviderRole"], 300L, "0xdef")

		// Combine logs from both blocks
		def mockPendingEventLogs = mockPendingEventLogsBlock200 + mockPendingEventLogsBlock300
		setUpPendingEvent(mockPendingEventLogs as List<EthLog.LogResult>)

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "No exceptions are thrown"
		noExceptionThrown()

		and: "Service logs error and restarts"
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Error in monitoring") }
		assert messages.any { it.contains("Restarting bc monitoring") }
		assert messages.any { it.contains("Event transaction hash is zero") }

		and: "Event are rejected and not saved to DynamoDB"
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		def roleGrantedEvent = eventsInDb.find { event ->
			event.get("name").s() == "roleGranted"
		}
		assert roleGrantedEvent == null
	}

	/**
	 * Should rejects events with empty transaction hash in pending events
	 * Verifies service rejects events with empty transaction hash
	 * Expected: Service rejects events with empty transaction hash, logs "Event transaction hash is zero" and monitoring continues with retry
	 */
	def "Should rejects events with empty transaction hash"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available in pending events"
		// Starting from block 1000, creating 3 notifications (blocks 1000, 1001, 1002)
		def mockNotifications = createMockNewHeadsNotifications(1000L, 3)
		setUpEventStream(mockNotifications)
		// Setup mock Web3j to return blocks with transactions and events
		def eventLogConfigs = [
			[logType: 'addProviderRole', txHash: '0xabc123', blockNumber: 1000L],
			[logType: 'addTokenByProvider', txHash: '0xdef456', blockNumber: 1001L],
			[logType: 'roleAdminChanged', txHash: '0x123abc', blockNumber: 1002L]
		]
		setupMockWeb3jWithEvents(eventLogConfigs)

		// Setup mock pending events with missing transaction hash
		// Create mock pending event logs from two different blocks
		def mockPendingEventLogsBlock200 = createMockPendingEventLogs(["roleGranted"], 200L, "")
		def mockPendingEventLogsBlock300 = createMockPendingEventLogs(["addProviderRole"], 300L, "0xdef")

		// Combine logs from both blocks
		def mockPendingEventLogs = mockPendingEventLogsBlock200 + mockPendingEventLogsBlock300
		setUpPendingEvent(mockPendingEventLogs as List<EthLog.LogResult>)

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "No exceptions are thrown"
		noExceptionThrown()

		and: "Service logs error and restarts"
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Error in monitoring") }
		assert messages.any { it.contains("Restarting bc monitoring") }
		assert messages.any { it.contains("Event transaction hash is zero") }

		and: "Event are rejected and not saved to DynamoDB"
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		def roleGrantedEvent = eventsInDb.find { event ->
			event.get("name").s() == "roleGranted"
		}
		assert roleGrantedEvent == null
	}

	/**
	 * Should service rejects events with missing transaction hash in new blocks
	 * Verifies service rejects events with missing transaction hash in new blocks
	 * Expected: Service rejects events with missing transaction hash in new blocks, logs "Error converting block to events: Transaction is null" and monitoring continues with retry
	 */
	def "Should rejects events with missing transaction hash in new blocks"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available"
		// Starting from block 1000, creating 3 notifications (blocks 1000, 1001, 1002)
		def mockNotifications = createMockNewHeadsNotifications(1000L, 3)
		setUpEventStream(mockNotifications)
		// Setup mock Web3j to return blocks with transactions and events
		def eventLogConfigs = [
			[logType: 'roleAdminChanged', txHash: null, blockNumber: 1002L]
		]
		setupMockWeb3jWithEvents(eventLogConfigs)

		// Setup mock pending events with empty transaction hash
		def mockPendingEventLogsBlock = createMockPendingEventLogs(["roleGranted"], 200L, "0x123")
		setUpPendingEvent(mockPendingEventLogsBlock)

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "No exceptions are thrown during execution"
		noExceptionThrown()

		and: "Service logs error and restarts"
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Error in monitoring") }
		assert messages.any { it.contains("Restarting bc monitoring") }
		assert messages.any { it.contains("Error converting block to events: Transaction is null") }

		and: "Event are rejected and not saved to DynamoDB"
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		def roleAdminChangedEvent = eventsInDb.find { event ->
			event.get("name").s() == "RoleAdminChanged"
		}
		assert roleAdminChangedEvent == null
	}

	/**
	 * Should service rejects events with empty transaction hash in new blocks
	 * Verifies service rejects events with empty transaction hash in new blocks
	 * Expected: Service rejects events with empty transaction hash in new blocks, logs "Error converting block to events: Transaction is null" and monitoring continues with retry
	 */
	def "Should rejects events with empty transaction hash in new blocks"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available"
		// Starting from block 1000, creating 3 notifications (blocks 1000, 1001, 1002)
		def mockNotifications = createMockNewHeadsNotifications(1000L, 3)
		setUpEventStream(mockNotifications)
		// Setup mock Web3j to return blocks with transactions and events
		def eventLogConfigs = [
			[logType: 'roleAdminChanged', txHash: '', blockNumber: 1002L]
		]
		setupMockWeb3jWithEvents(eventLogConfigs)

		// Setup mock pending events with empty transaction hash
		def mockPendingEventLogsBlock = createMockPendingEventLogs(["roleGranted"], 200L, "0x123")
		setUpPendingEvent(mockPendingEventLogsBlock)

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "No exceptions are thrown during execution"
		noExceptionThrown()

		and: "Service logs error and restarts"
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Error in monitoring") }
		assert messages.any { it.contains("Restarting bc monitoring") }
		assert messages.any { it.contains("Error converting block to events: Transaction is null") }

		and: "Event are rejected and not saved to DynamoDB"
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		def roleAdminChangedEvent = eventsInDb.find { event ->
			event.get("name").s() == "RoleAdminChanged"
		}
		assert roleAdminChangedEvent == null
	}

	/**
	 * Should rejects blocks with zero block number
	 * Verifies service rejects blocks with zero block number
	 * Expected: Service rejects blocks with zero block number, logs "Block height Number is zero" and monitoring continues with retry
	 */
	def "Should rejects blocks with zero block number"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available"
		// Starting from block 1000, creating 3 notifications (blocks 1000, 1001, 1002)
		def mockNotifications = createMockNewHeadsNotifications(1000L, 3)
		setUpEventStream(mockNotifications)
		// Setup mock Web3j to return blocks with transactions and events
		def eventLogConfigs = [
			[logType: 'roleAdminChanged', txHash: '0x123abc', blockNumber: 1002L]
		]
		setupMockWeb3jWithEvents(eventLogConfigs)

		// Setup mock pending events with zero block number
		def mockPendingEventLogsBlock = createMockPendingEventLogs(["roleGranted"], 0L, "0x231")
		setUpPendingEvent(mockPendingEventLogsBlock)

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "No exceptions are thrown"
		noExceptionThrown()

		and: "Service logs error and restarts"
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Error in monitoring") }
		assert messages.any { it.contains("Restarting bc monitoring") }
		assert messages.any { it.contains("Block height Number is zero") }
	}

	/**
	 * Should processes events with tuple types correctly
	 * Verifies service correctly processes events with tuple types during ABI parsing
	 * Expected: Transfer event with tuple types is parsed correctly during ABI processing
	 */
	def "Should processes events with tuple types correctly"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available"
		// Starting from block 1000, creating 2 notifications (blocks 1000, 1001)
		def mockNotifications = createMockNewHeadsNotifications(1000L, 2)
		setUpEventStream(mockNotifications)
		// Setup mock Web3j to return blocks with transactions and events (no Transfer event to avoid conversion issues)
		def eventLogConfigs = [
			[logType: 'transfer', txHash: '0xdef456', blockNumber: 1001L]
		]
		setupMockWeb3jWithEvents(eventLogConfigs)

		// Setup mock pending events (no Transfer event to avoid conversion issues)
		def mockPendingEventLogs = createMockPendingEventLogs([
			"transfer"
		], 200L, "0xtransfer")
		setUpPendingEvent(mockPendingEventLogs)

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "No exceptions are thrown"
		noExceptionThrown()

		and: "Service starts and processes ABI files successfully"
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Started bc monitoring") }
		assert messages.any { it.contains("Monitoring events...") }

		and: "Transfer event with tuple types is parsed during ABI processing"
		// Verify that tuple processing occurred during ABI parsing
		assert messages.any { it.contains("Processing tuple type: tuple") }
		assert messages.any { it.contains("Extracted 17 components from tuple type tuple") }
		assert messages.any { it.contains("Parsed event: Transfer with signature:") }

		// Verify that the Transfer event was registered
		assert messages.any { it.contains("Successfully parsed ABI with 10 events") }
		assert messages.any { it.contains("Registered events for contract: Token") }

		and: "Other events are processed and saved to DynamoDB"
		// Check if events were saved to DynamoDB
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		println("Events found in DynamoDB: ${eventsInDb?.size() ?: 'null'}")
		if (eventsInDb != null) {
			eventsInDb.each { event ->
				println("Event: ${event}")
			}
		}

		// Verify that at least some events were processed (AddTokenByProvider)
		if (eventsInDb != null && eventsInDb.size() > 0) {
			def addTokenEvents = eventsInDb.findAll { event ->
				event.get("name").s() == "AddTokenByProvider"
			}
			assert addTokenEvents.size() >= 1
		}

		and: "Block height is updated"
		def blockHeightInDb = AdhocHelper.scanBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
		println("Block height in DynamoDB: ${blockHeightInDb}")
		// Verify that block height was processed
		blockHeightInDb != null
	}
}
