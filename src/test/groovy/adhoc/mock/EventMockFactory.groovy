package adhoc.mock

import org.web3j.abi.EventEncoder
import org.web3j.abi.TypeReference
import org.web3j.abi.datatypes.Address
import org.web3j.abi.datatypes.DynamicBytes
import org.web3j.abi.datatypes.Event
import org.web3j.abi.datatypes.Utf8String
import org.web3j.abi.datatypes.generated.Bytes32
import org.web3j.protocol.core.methods.response.Log

/**
 * Factory class for creating mock event objects used in testing.
 * This class centralizes the creation of various mock objects related to blockchain events,
 * improving maintainability and reusability across test classes.
 *
 * Note: This class provides data and configuration for mocks, but the actual Mock() creation
 * must be done within Spock test classes.
 */
class EventMockFactory {
	// converts to "TEST_PROVIDER_ID"
	public static final String PROVIDER_ID_HEX = "0x544553545f50524f56494445525f494400000000000000000000000000000000"

	// providerEoa: ****************************************** (20 bytes)
	public static final String PROVIDER_EOA_HEX_20_BYTES = "******************************************"

	// traceId: converts to "TRACE_ADD_PROVIDER" (32 bytes)
	public static final String TRACE_ADD_PROVIDER = "54524143455f4144445f50524f56494445520000000000000000000000000000"

	// tokenId: converts to "TEST_TOKEN_ID"
	public static final String TOKEN_ID_HEX = "0x544553545f544f4b454e5f494400000000000000000000000000000000000000"

	// traceId: converts to "TRACE_ADD_TOKEN"
	public static final String TRACE_ADD_TOKEN_HEX = "54524143455f4144445f544f4b454e0000000000000000000000000000000000"

	// traceId: converts to "TRACE_TRANSFER"
	public static final String TRACE_TRANSFER_HEX = "54524143455f5452414e534645520000000000000000000000000000000000"
	/**
	 * Create a mock AddProviderRole event definition for testing
	 * Event: AddProviderRole(bytes32 indexed providerId, address providerEoa, bytes32 traceId)
	 * @return Event definition for AddProviderRole
	 */
	static Event createMockAddProviderRoleEvent() {
		def parameters = [
			// providerId (indexed)
			new TypeReference<Bytes32>(true) {},
			// providerEoa (non-indexed)
			new TypeReference<Address>(false) {},
			// traceId (non-indexed)
			new TypeReference<Bytes32>(false) {}
		]
		return new Event("AddProviderRole", parameters)
	}

	/**
	 * Create a mock AddTokenByProvider event definition for testing
	 * Event: AddTokenByProvider(bytes32 indexed providerId, bytes32 tokenId, bytes32 traceId)
	 * @return Event definition for AddTokenByProvider
	 */
	static Event createMockAddTokenByProviderEvent() {
		def parameters = [
			// providerId (indexed)
			new TypeReference<Bytes32>(true) {},
			// tokenId (non-indexed)
			new TypeReference<Bytes32>(false) {},
			// traceId (non-indexed)
			new TypeReference<Bytes32>(false) {}
		]
		return new Event("AddTokenByProvider", parameters)
	}

	/**
	 * Create a mock AddRoleAdminChanged event definition for testing
	 * Event: AddRoleAdminChanged(bytes32 indexed role, bytes32 indexed previousAdminRole, bytes32 indexed newAdminRole)
	 * @return Event definition for AddRoleAdminChanged
	 */
	static Event createMockAddRoleAdminChangedEvent() {
		def parameters = [
			// role (indexed)
			new TypeReference<Bytes32>(true) {},
			// previousAdminRole (indexed)
			new TypeReference<Bytes32>(true) {},
			// newAdminRole (indexed)
			new TypeReference<Bytes32>(true) {}
		]
		return new Event("RoleAdminChanged", parameters)
	}

	/**
	 * Create a mock RoleGranted event definition for testing
	 * Event: RoleGranted(bytes32 indexed role, address indexed account, address indexed sender)
	 * @return Event definition for RoleGranted
	 */
	static Event createMockRoleGrantedEvent() {
		def parameters = [
			// role (indexed)
			new TypeReference<Bytes32>(true) {},
			// account (indexed)
			new TypeReference<Address>(true) {},
			// sender (indexed)
			new TypeReference<Address>(true) {}
		]
		return new Event("RoleGranted", parameters)
	}

	/**
	 * Create a mock RoleRevoked event definition for testing
	 * Event: RoleRevoked(bytes32 indexed role, address indexed account, address indexed sender)
	 * @return Event definition for RoleRevoked
	 */
	static Event createMockRoleRevokedEvent() {
		def parameters = [
			// role (indexed)
			new TypeReference<Bytes32>(true) {},
			// account (indexed)
			new TypeReference<Address>(true) {},
			// sender (indexed)
			new TypeReference<Address>(true) {}
		]
		return new Event("RoleRevoked", parameters)
	}

	/**
	 * Create a mock ModProvider event definition for testing
	 * Event: ModProvider(bytes32 indexed providerId, bytes32 name, bytes32 traceId)
	 * @return Event definition for ModProvider
	 */
	static Event createMockModProviderEvent() {
		def parameters = [
			// providerId (indexed)
			new TypeReference<Bytes32>(true) {},
			// name (non-indexed)
			new TypeReference<Bytes32>(false) {},
			// traceId (non-indexed)
			new TypeReference<Bytes32>(false) {}
		]
		return new Event("ModProvider", parameters)
	}

	/**
	 * Create a mock ModAccount event definition for testing
	 * Event: ModAccount(bytes32 accountId, string accountName, bytes32 traceId)
	 * @return Event definition for ModAccount
	 */
	static Event createMockModAccountEvent() {
		def parameters = [
			// accountId (non-indexed)
			new TypeReference<Bytes32>(false) {},
			// accountName (non-indexed)
			new TypeReference<Utf8String>(false) {},
			// traceId (non-indexed)
			new TypeReference<Bytes32>(false) {}
		]
		return new Event("ModAccount", parameters)
	}

	/**
	 * Create a mock Transfer event definition for testing
	 * Event: Transfer(tuple transferData, bytes32 traceId)
	 * Note: This is a simplified version for testing tuple handling
	 * @return Event definition for Transfer
	 */
	static Event createMockTransferEvent() {
		def parameters = [
                // transferData (tuple, non-indexed) - using DynamicBytes as fallback for tuple
                new TypeReference<DynamicBytes>(false) {},
                // traceId (non-indexed)
                new TypeReference<Bytes32>(false) {}
		]
		return new Event("Transfer", parameters)
	}

	/**
	 * Create a proper AddTokenByProvider log for normal testing
	 * This matches the exact structure expected by the ABI parser
	 * @return Log object with AddTokenByProvider event data
	 */
	static Log createAddTokenByProviderLog() {
		def log = new Log()

		// Use Provider contract address
		log.address = "0xb0BDD71BdB22B3d0b3B6dD8C47DF0f3C658eA22A"

		// Calculate AddTokenByProvider event signature using Web3j
		def addTokenByProviderEvent = createMockAddTokenByProviderEvent()
		def eventSignature = EventEncoder.encode(addTokenByProviderEvent)
		println("AddTokenByProvider event signature: ${eventSignature}")

		log.topics = [eventSignature, PROVIDER_ID_HEX]

		// Data contains: bytes32 tokenId + bytes32 traceId
		// tokenId: converts to "TEST_TOKEN_ID"
		// traceId: converts to "TRACE_ADD_TOKEN"
		log.data = TOKEN_ID_HEX + TRACE_ADD_TOKEN_HEX

		return log
	}

	/**
	 * Create a proper AddProviderRole log for normal testing
	 * This matches the exact structure expected by the ABI parser
	 * @return Log object with AddProviderRole event data
	 */
	static Log createAddProviderRoleLog() {
		def log = new Log()

		// Use Provider contract address
		log.address = "0xb0BDD71BdB22B3d0b3B6dD8C47DF0f3C658eA22A"

		// Calculate AddProviderRole event signature using Web3j
		def addProviderRoleEvent = createMockAddProviderRoleEvent()
		def eventSignature = EventEncoder.encode(addProviderRoleEvent)
		println("AddProviderRole event signature: ${eventSignature}")

		// providerId (indexed parameter) - converts to "TEST_PROVIDER_ID"
		def providerId = PROVIDER_ID_HEX

		log.topics = [eventSignature, providerId]

		// Data contains: address providerEoa + bytes32 traceId
		// providerEoa: ****************************************** (20 bytes, padded to 32)
		// traceId: converts to "TRACE_ADD_PROVIDER" (32 bytes)
		log.data = to32Bytes(PROVIDER_EOA_HEX_20_BYTES) + TRACE_ADD_PROVIDER

		return log
	}

	/**
	 * Create a proper RoleAdminChanged log for case empty traceId in non-indexed values
	 * This matches the exact structure expected by the ABI parser
	 * @return Log object with RoleAdminChanged event data
	 */
	static Log createRoleAdminChangedLog() {
		def log = new Log()

		// Use AccessControl contract address
		log.address = "0xF27289E45825f7E8F3eAE5c3F52e05c8FB6fD3d4"

		// Calculate RoleAdminChanged event signature using Web3j
		def event = createMockAddRoleAdminChangedEvent()
		def eventSignature = EventEncoder.encode(event)
		println("RoleAdminChanged event signature: ${eventSignature}")

		def role = PROVIDER_ID_HEX // "TEST_PROVIDER_ID"
		def previousAdminRole = PROVIDER_ID_HEX // "TEST_PROVIDER_ID"
		def newAdminRole = PROVIDER_ID_HEX // "TEST_PROVIDER_ID"

		log.topics = [
			eventSignature,
			role,
			previousAdminRole,
			newAdminRole
		]

		return log
	}

	/**
	 * Create a proper RoleGranted log for testing
	 * This matches the exact structure expected by the ABI parser
	 * @return Log object with RoleGranted event data
	 */
	static Log createMockRoleGrantedLog() {
		def log = new Log()

		// Use AccessControl contract address
		log.address = "0xF27289E45825f7E8F3eAE5c3F52e05c8FB6fD3d4"

		// Calculate RoleGranted event signature using Web3j
		def event = createMockRoleGrantedEvent()
		def eventSignature = EventEncoder.encode(event)
		println("RoleGranted event signature: ${eventSignature}")

		def role = PROVIDER_ID_HEX // "TEST_PROVIDER_ID"
		def account = "0x544553545f4143434f554e545f49440000000000000000000000000000000000" // "TEST_ACCOUNT_ID"
		def sender = PROVIDER_ID_HEX // "TEST_PROVIDER_ID"

		log.topics = [
			eventSignature,
			role,
			account,
			sender
		]

		return log
	}

	/**
	 * Create a proper RoleRevoked log for testing
	 * This matches the exact structure expected by the ABI parser
	 * @return Log object with RoleRevoked event data
	 */
	static Log createMockRoleRevokedLog() {
		def log = new Log()

		// Use AccessControl contract address
		log.address = "0xF27289E45825f7E8F3eAE5c3F52e05c8FB6fD3d4"

		// Calculate RoleRevoked event signature using Web3j
		def event = createMockRoleRevokedEvent()
		def eventSignature = EventEncoder.encode(event)
		println("RoleRevoked event signature: ${eventSignature}")

		def role = PROVIDER_ID_HEX // "TEST_PROVIDER_ID"
		def account = "0x544553545f4143434f554e545f49440000000000000000000000000000000000" // "TEST_ACCOUNT_ID"
		def sender = PROVIDER_ID_HEX // "TEST_PROVIDER_ID"

		log.topics = [
			eventSignature,
			role,
			account,
			sender
		]

		return log
	}

	/** Create a proper ModProvider log for testing
	 * This matches the exact structure expected by the ABI parser
	 * @return Log object with ModProvider event data
	 */
	static Log createMockModProviderLog() {
		def log = new Log()

		// Use Provider contract address
		log.address = "0xb0BDD71BdB22B3d0b3B6dD8C47DF0f3C658eA22A"

		// Calculate ModProvider event signature using Web3j
		def modProviderEvent = createMockModProviderEvent()
		def eventSignature = EventEncoder.encode(modProviderEvent)
		println("ModProvider event signature: ${eventSignature}")

		// providerId (indexed parameter) - converts to "TEST_PROVIDER_ID"
		def providerId = PROVIDER_ID_HEX

		log.topics = [eventSignature, providerId]

		// Data contains: bytes32 name + bytes32 traceId
		// name: converts to "PROVIDER_NAME"
		// traceId: converts to "TRACE_MOD_PROVIDER"
		log.data = "0x50524f56494445525f4e414d4500000000000000000000000000000000000000" +
				"54524143455f4d4f445f50524f56494445520000000000000000000000000000"

		return log
	}

	/**
	 * Create a proper ModAccount log for testing
	 * This matches the exact structure expected by the ABI parser
	 * @return Log object with ModAccount event data
	 */
	static Log createMockModAccountLog() {
		def log = new Log()

		// Use Account contract address
		log.address = "0x993366A606A99129e56B4b99B27e428ba1Cb672f"

		// Calculate ModAccount event signature using Web3j
		def modAccountEvent = createMockModAccountEvent()
		def eventSignature = EventEncoder.encode(modAccountEvent)
		println("ModAccount event signature: ${eventSignature}")

		// ModAccount has no indexed parameters, so topics only contains event signature
		log.topics = [eventSignature]

		// Data contains: bytes32 accountId + string accountName + bytes32 traceId
		// For ABI encoding with parameters (bytes32, string, bytes32):
		// - First 32 bytes: accountId (fixed size, goes first)
		// - Next 32 bytes: offset to string data (0x60 = 96 bytes, pointing after all fixed-size params)
		// - Next 32 bytes: traceId (fixed size)
		// - At offset 0x60: string length (32 bytes) + string data (padded to 32-byte boundary)

		def accountId = "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
		def traceId = "ef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcd"
		def accountName = "TestAccount"
		def accountNameHex = accountName.bytes.encodeHex().toString().padRight(64, '0')
		def accountNameLength = String.format("%064x", accountName.length())

		log.data = "0x" +
				accountId + // accountId (bytes32)
				"0000000000000000000000000000000000000000000000000000000000000060" + // offset to string (96 bytes)
				traceId + // traceId (bytes32)
				accountNameLength + // string length
				accountNameHex // string data

		return log
	}

	/**
	 * Create a proper Transfer log for testing tuple types
	 * This matches the exact structure expected by the ABI parser
	 * @return Log object with Transfer event data containing tuple
	 */
	static Log createMockTransferLog() {
		def log = new Log()

		// Use Token contract address
		log.address = "0x88eEA3e4F0839B74A8dE27951bc630126837d646"

		// Calculate Transfer event signature using Web3j
		def transferEvent = createMockTransferEvent()
		def eventSignature = EventEncoder.encode(transferEvent)
		println("Transfer event signature: ${eventSignature}")

		// Transfer has no indexed parameters, so topics only contains event signature
		log.topics = [eventSignature]

		// Data contains: tuple transferData + bytes32 traceId
		// For tuple handling, we'll create a simplified structure that represents the tuple data
		// The actual tuple will be processed by the ABI parser using the real Token.json ABI

		// Create mock tuple data (simplified representation)
		// In real scenario, this would be properly encoded tuple data
		def transferType = "0x5452414e534645525f54595045000000000000000000000000000000000000" // "TRANSFER_TYPE"
		def zoneId = String.format("%064x", 1) // uint16 zoneId = 1
		def fromValidatorId = "0x46524f4d5f56414c494441544f525f4944000000000000000000000000000000" // "FROM_VALIDATOR_ID"
		def toValidatorId = "0x544f5f56414c494441544f525f494400000000000000000000000000000000" // "TO_VALIDATOR_ID"
		def fromAccountBalance = String.format("%064x", 1000) // uint256 = 1000
		def toAccountBalance = String.format("%064x", 2000) // uint256 = 2000
		def businessZoneBalance = String.format("%064x", 5000) // uint256 = 5000
		def bizZoneId = String.format("%064x", 2) // uint16 bizZoneId = 2
		def sendAccountId = "0x53454e445f4143434f554e545f494400000000000000000000000000000000" // "SEND_ACCOUNT_ID"
		def fromAccountId = "0x46524f4d5f4143434f554e545f494400000000000000000000000000000000" // "FROM_ACCOUNT_ID"
		def toAccountId = "0x544f5f4143434f554e545f49440000000000000000000000000000000000" // "TO_ACCOUNT_ID"
		def amount = String.format("%064x", 500) // uint256 = 500
		def miscValue1 = "0x4d4953435f56414c5545315f000000000000000000000000000000000000000" // "MISC_VALUE1_"

		// For string fields, we need to encode them properly with length and offset
		def fromAccountName = "FromAccount"
		def toAccountName = "ToAccount"
		def miscValue2 = "MiscValue2"
		def memo = "TestMemo"

		// Calculate offsets for dynamic data (strings)
		def baseOffset = 17 * 32 // 17 fixed-size fields * 32 bytes each
		def fromAccountNameOffset = String.format("%064x", baseOffset)
		def toAccountNameOffset = String.format("%064x", baseOffset + 64) // after fromAccountName length + data
		def miscValue2Offset = String.format("%064x", baseOffset + 128) // after previous strings
		def memoOffset = String.format("%064x", baseOffset + 192) // after previous strings

		// Encode strings with length prefix
		def fromAccountNameHex = fromAccountName.bytes.encodeHex().toString().padRight(64, '0')
		def fromAccountNameLength = String.format("%064x", fromAccountName.length())
		def toAccountNameHex = toAccountName.bytes.encodeHex().toString().padRight(64, '0')
		def toAccountNameLength = String.format("%064x", toAccountName.length())
		def miscValue2Hex = miscValue2.bytes.encodeHex().toString().padRight(64, '0')
		def miscValue2Length = String.format("%064x", miscValue2.length())
		def memoHex = memo.bytes.encodeHex().toString().padRight(64, '0')
		def memoLength = String.format("%064x", memo.length())

		// Build the complete data field
		log.data = "0x" +
			// Offset to tuple data (starts immediately)
			"0000000000000000000000000000000000000000000000000000000000000040" + // offset to transferData tuple
			TRACE_TRANSFER_HEX + // traceId (bytes32)
			// Tuple data starts here
			transferType + // bytes32 transferType
			zoneId + // uint16 zoneId (padded to 32 bytes)
			fromValidatorId + // bytes32 fromValidatorId
			toValidatorId + // bytes32 toValidatorId
			fromAccountBalance + // uint256 fromAccountBalance
			toAccountBalance + // uint256 toAccountBalance
			businessZoneBalance + // uint256 businessZoneBalance
			bizZoneId + // uint16 bizZoneId (padded to 32 bytes)
			sendAccountId + // bytes32 sendAccountId
			fromAccountId + // bytes32 fromAccountId
			fromAccountNameOffset + // offset to fromAccountName string
			toAccountId + // bytes32 toAccountId
			toAccountNameOffset + // offset to toAccountName string
			amount + // uint256 amount
			miscValue1 + // bytes32 miscValue1
			miscValue2Offset + // offset to miscValue2 string
			memoOffset + // offset to memo string
			// String data
			fromAccountNameLength + fromAccountNameHex + // fromAccountName
			toAccountNameLength + toAccountNameHex + // toAccountName
			miscValue2Length + miscValue2Hex + // miscValue2
			memoLength + memoHex // memo

		return log
	}

	/**
	 * Convert a 20-byte address to 32-byte hex string
	 *
	 * @param address20Bytes String
	 * @return the 32-byte hex string
	 */
	private static String to32Bytes(String address20Bytes) {
		if (address20Bytes == null) return null
		String hex = address20Bytes.toLowerCase().replace("0x", "")
		if (hex.length() != 40) throw new IllegalArgumentException("Not a valid 20-byte address")
		return "0x" + String.format("%064x", new BigInteger(hex, 16))
	}
}
