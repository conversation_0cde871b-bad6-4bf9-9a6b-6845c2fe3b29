{"address": "0xb0BDD71BdB22B3d0b3B6dD8C47DF0f3C658eA22A", "abi": [{"type": "event", "anonymous": false, "name": "AddBizZone", "inputs": [{"type": "uint16", "name": "zoneId", "indexed": false}, {"type": "string", "name": "zoneName", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "AddProvider", "inputs": [{"type": "bytes32", "name": "providerId", "indexed": true}, {"type": "uint16", "name": "zoneId", "indexed": false}, {"type": "string", "name": "zoneName", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "AddProviderRole", "inputs": [{"type": "bytes32", "name": "providerId", "indexed": true}, {"type": "address", "name": "providerEoa", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "AddTokenByProvider", "inputs": [{"type": "bytes32", "name": "providerId", "indexed": true}, {"type": "bytes32", "name": "tokenId", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "Initialized", "inputs": [{"type": "uint8", "name": "version", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": [{"type": "bytes32", "name": "providerId", "indexed": true}, {"type": "bytes32", "name": "name", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "ModZone", "inputs": [{"type": "bytes32", "name": "providerId", "indexed": false}, {"type": "string", "name": "zoneName", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "ProviderEnabled", "inputs": [{"type": "bytes32", "name": "providerId", "indexed": true}, {"type": "bool", "name": "enabled", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "SetTokenIdByProvider", "inputs": [{"type": "bytes32", "name": "providerId", "indexed": true}, {"type": "bytes32", "name": "tokenId", "indexed": false}, {"type": "bytes32", "name": "traceId", "indexed": false}]}, {"type": "function", "name": "ROLE_PREFIX_PROV", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "bytes32", "name": ""}]}, {"type": "function", "name": "addBizZone", "constant": false, "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}, {"type": "string", "name": "zoneName"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "addProvider", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "providerId"}, {"type": "uint16", "name": "zoneId"}, {"type": "string", "name": "zoneName"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "addProviderRole", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "providerId"}, {"type": "address", "name": "providerEoa"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "addToken", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "providerId"}, {"type": "bytes32", "name": "tokenId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "symbol"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "checkRole", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "providerId"}, {"type": "bytes32", "name": "hash"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": [{"type": "bool", "name": "has"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "get<PERSON><PERSON><PERSON>", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "bytes32", "name": "providerId"}, {"type": "uint16", "name": "zoneId"}, {"type": "string", "name": "zoneName"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getProviderAll", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "providerId"}], "outputs": [{"type": "tuple", "name": "provider", "components": [{"type": "bytes32", "name": "providerId"}, {"type": "tuple", "name": "providerData", "components": [{"type": "bytes32", "name": "role"}, {"type": "bytes32", "name": "name"}, {"type": "uint16", "name": "zoneId"}, {"type": "bool", "name": "enabled"}]}, {"type": "address", "name": "providerEoa"}, {"type": "tuple[]", "name": "zoneData", "components": [{"type": "uint16", "name": "zoneId"}, {"type": "string", "name": "zoneName"}]}]}]}, {"type": "function", "name": "getProviderCount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "uint256", "name": "count"}]}, {"type": "function", "name": "getToken", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "providerId"}], "outputs": [{"type": "bytes32", "name": "tokenId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "symbol"}, {"type": "uint256", "name": "totalSupply"}, {"type": "bool", "name": "enabled"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getTokenId", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "bytes32", "name": "tokenId"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getZone", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "uint16", "name": "zoneId"}, {"type": "string", "name": "zoneName"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "getZoneName", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}], "outputs": [{"type": "string", "name": "zoneName"}]}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON>", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "providerId"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "hasToken", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "tokenId"}, {"type": "bytes32", "name": "providerId"}, {"type": "bool", "name": "chkEnabled"}], "outputs": [{"type": "bool", "name": "success"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "initialize", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "contractManager"}], "outputs": []}, {"type": "function", "name": "mod<PERSON><PERSON>ider", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "providerId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "modToken", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "tokenId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "symbol"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "modZone", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "providerId"}, {"type": "string", "name": "zoneName"}, {"type": "bytes32", "name": "traceId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "setProviderAll", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "provider", "components": [{"type": "bytes32", "name": "providerId"}, {"type": "tuple", "name": "providerData", "components": [{"type": "bytes32", "name": "role"}, {"type": "bytes32", "name": "name"}, {"type": "uint16", "name": "zoneId"}, {"type": "bool", "name": "enabled"}]}, {"type": "address", "name": "providerEoa"}, {"type": "tuple[]", "name": "zoneData", "components": [{"type": "uint16", "name": "zoneId"}, {"type": "string", "name": "zoneName"}]}]}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "version", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "transactionHash": "0x1ce6422a146b6bc2618fc3bdd141e90dd7e5d983c93bd301c6e8d7c7de9f254f", "receipt": {"to": null, "from": "0x027CaDD533C40e8017de7ac814baE35f8b08f402", "contractAddress": "0xb0BDD71BdB22B3d0b3B6dD8C47DF0f3C658eA22A", "transactionIndex": 0, "gasUsed": "3472874", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0x1b4fac4d83656a6de880dffa6cb49d2be3d2c623a99b8b3ce039b6030c811b60", "blockNumber": 317, "cumulativeGasUsed": "3472874", "status": 1}}